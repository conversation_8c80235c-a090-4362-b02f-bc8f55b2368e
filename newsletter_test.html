<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Newsletter Subscription Component Test</title>
    <style>
        /* Variables from Mirraw red theme */
        :root {
            --dark-red: #670b19;
            --light-red: #b11f2d;
            --text-white: #ffffff;
            --text-black: #303030;
            --placeholder-color: #615f5f;
            --gray: #4d4d4d;
            --add-to-cart-red: #8f1b1d;
        }
        
        body {
            font-family: "Inter", sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        /* Newsletter Subscription Desktop Component */
        .newsletter-subscription-desktop {
            background: linear-gradient(135deg, var(--dark-red) 0%, var(--light-red) 100%);
            padding: 40px 20px;
            margin: 0;
            display: none; /* Hidden by default (mobile-first) */
        }
        
        .newsletter-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            align-items: center;
            justify-content: space-between;
            flex-wrap: wrap;
            gap: 30px;
        }
        
        .newsletter-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
            gap: 30px;
        }
        
        .newsletter-text {
            flex: 1;
            color: var(--text-white);
        }
        
        .newsletter-title {
            font-size: 28px;
            font-weight: 700;
            margin: 0 0 8px 0;
            line-height: 1.2;
            font-family: "Inter", sans-serif;
        }
        
        .newsletter-subtitle {
            font-size: 16px;
            margin: 0;
            opacity: 0.9;
            line-height: 1.4;
        }
        
        .newsletter-form-wrapper {
            flex: 0 0 auto;
            min-width: 400px;
        }
        
        .newsletter-form .form-group {
            display: flex;
            gap: 0;
            background: var(--text-white);
            border-radius: 6px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        
        .newsletter-email-input {
            flex: 1;
            padding: 16px 20px;
            border: none;
            font-size: 16px;
            background: transparent;
            color: var(--text-black);
        }
        
        .newsletter-email-input::placeholder {
            color: var(--placeholder-color);
            opacity: 0.7;
        }
        
        .newsletter-email-input:focus {
            outline: none;
            box-shadow: none;
        }
        
        .newsletter-submit-btn {
            background: var(--add-to-cart-red);
            color: var(--text-white);
            border: none;
            padding: 16px 30px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.3s ease;
            white-space: nowrap;
        }
        
        .newsletter-submit-btn:hover {
            background: #7a1619;
        }
        
        .newsletter-submit-btn:disabled {
            background: var(--gray);
            cursor: not-allowed;
        }
        
        .newsletter-message {
            margin-top: 12px;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 14px;
            text-align: center;
            display: none;
        }
        
        .newsletter-message.success {
            background: rgba(255, 255, 255, 0.2);
            color: var(--text-white);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        
        .newsletter-message.error {
            background: rgba(255, 255, 255, 0.9);
            color: var(--dark-red);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        
        /* Show only on desktop (1024px and above) */
        @media screen and (min-width: 1024px) {
            .newsletter-subscription-desktop {
                display: block;
            }
        }
        
        /* Responsive adjustments for smaller desktop screens */
        @media screen and (min-width: 1024px) and (max-width: 1200px) {
            .newsletter-content {
                flex-direction: column;
                text-align: center;
                gap: 25px;
            }
            
            .newsletter-title {
                font-size: 24px;
            }
            
            .newsletter-subtitle {
                font-size: 15px;
            }
            
            .newsletter-form-wrapper {
                min-width: 350px;
            }
        }
        
        .demo-info {
            background: white;
            padding: 20px;
            margin-bottom: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .demo-footer {
            background: #333;
            color: white;
            padding: 40px 20px;
            text-align: center;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="demo-info">
        <h1>Newsletter Subscription Component Test</h1>
        <p><strong>Instructions:</strong></p>
        <ul>
            <li>This component is designed to show only on desktop screens (≥1024px width)</li>
            <li>Resize your browser window to test responsive behavior</li>
            <li>On mobile/tablet, the component should be hidden</li>
            <li>The component appears above the footer section</li>
        </ul>
        <p><strong>Current screen width:</strong> <span id="screen-width"></span>px</p>
        <p><strong>Component visibility:</strong> <span id="component-status"></span></p>
    </div>

    <!-- Newsletter Subscription Component -->
    <div class="newsletter-subscription-desktop d_web">
        <div class="newsletter-container">
            <div class="newsletter-content">
                <div class="newsletter-text">
                    <h2 class="newsletter-title">Sign Up to Receive Our Updates</h2>
                    <p class="newsletter-subtitle">Be the first to know about latest offers and discounts on Mirraw</p>
                </div>
                <div class="newsletter-form-wrapper">
                    <form id="desktop-newsletter-form" class="newsletter-form">
                        <div class="form-group">
                            <input type="email" name="subscriptions[email]" placeholder="Enter your Email Address" class="newsletter-email-input" id="newsletter-email-input" required>
                            <input type="submit" value="Subscribe!" class="newsletter-submit-btn" id="newsletter-submit-btn">
                        </div>
                        <input type="hidden" name="subscriptions[source_url]" value="http://localhost:3000">
                        <input type="hidden" name="subscriptions[appsource]" value="desktop">
                    </form>
                    <div class="newsletter-message" id="newsletter-message"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Demo Footer -->
    <div class="demo-footer">
        <h3>Footer Section</h3>
        <p>This represents the footer. The newsletter component should appear above this section.</p>
    </div>

    <script>
        function updateScreenInfo() {
            const width = window.innerWidth;
            document.getElementById('screen-width').textContent = width;
            
            const component = document.querySelector('.newsletter-subscription-desktop');
            const isVisible = window.getComputedStyle(component).display !== 'none';
            document.getElementById('component-status').textContent = isVisible ? 'Visible' : 'Hidden';
            document.getElementById('component-status').style.color = isVisible ? 'green' : 'red';
        }
        
        window.addEventListener('resize', updateScreenInfo);
        updateScreenInfo();
        
        // Demo form submission
        document.getElementById('desktop-newsletter-form').addEventListener('submit', function(e) {
            e.preventDefault();
            const messageDiv = document.getElementById('newsletter-message');
            messageDiv.className = 'newsletter-message success';
            messageDiv.textContent = 'Demo: Form submission successful! (This is just a visual test)';
            messageDiv.style.display = 'block';
            
            setTimeout(() => {
                messageDiv.style.display = 'none';
            }, 3000);
        });
    </script>
</body>
</html>
