body.modal-open{
  overflow: hidden;
  position: fixed;
}
body {
  font-family: "Inter", sans-serif !important;
}
#mobile_footer{
  // background-image: image-url('theme_bg.png');
  padding-top:10px;
  padding-bottom:1px;
  background-color: $dark_red;
  z-index: 1;

  #share {
    width: 100%;
    text-align: center;
    padding-bottom: 10px;
  }
  
  /* buttons */
  
  #share a {
    width: 50px;
    height: 50px;
    display: inline-block;
    margin: 8px;
    border-radius: 50%;
    font-size: 24px;
    color: #fff;
  }
  
  #share a:hover {
    opacity: 1;
  }
  
  /* icons */
  
  #share i {
      position: relative;
      top: 40%;
      transform: translateY(-50%);
  }
  
  /* colors */
  
  .facebook {
     background: #3b5998;
  }
  
  .instagram {
    background: #f09433; 
    background: -moz-linear-gradient(45deg, #f09433 0%,#e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%); 
    background: -webkit-linear-gradient(45deg, #f09433 0%,#e6683c 25%,#dc2743 50%,#cc2366 75%,#bc1888 100%); 
    background: linear-gradient(45deg, #f09433 0%,#e6683c 25%,#dc2743 50%,#cc2366 75%,#bc1888 100%); 
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#f09433', endColorstr='#bc1888',GradientType=1 );
  }
  
  
  .youtube {
    background: #cb2027;
  }

  .sign_in_button{
    background: #b11f2d; 
    margin-left: 34%; 
    padding: 3px;
  }
  .row div{
    text-align: center;
    margin:0 auto;
  }

  img{
    display: inline-block !important;
  }

  span{
    color: white;
    font-size: $font_size;
  }

  #footer_nav{
    margin-top: 12px;
    margin-bottom: 12px;
    margin-left:0px;
    .phoneNumber{
      padding: 0px; 
      font-size: $font_size;
      a{
        color: $text_white;
      }
    }
  }

  #footer_nav li{
    float: left;
    text-align: center;
    background-color: $light_red;
    width: 100%;
    list-style: none;
    font-size: $font_size;
    color: $text_white;
  }

  #footer_nav a{
    color: text_white !important;
    text-decoration: none;
    font-size: $font_size;
  }

  #footer_nav li:first-child{
    border-right:0px;
    padding-bottom: 0px; 
  }

  .expand ul{
    display: block !important;
  }

  .f_free,
  .f_world_wide,
  .f_contact,
  .f_money_back
  {
    display: inline-block;
    width: 36px;
    height: 35px;
    background: image-url('sprite.png') no-repeat;
  }

  .f_free{
    background-position: 4% 58%;
    width: 45px;
  }

  .f_world_wide{
    background-position: 50% 45%;
  }

  .f_contact{
    background-position: -3px -359px;
  }

  .f_money_back{
    background-position: 98% 54%;
  }

  .visible-xs {
    display: block !important;
  }


  .nopadding{
    padding-left:0px;
    padding-right:0px;
  }

  .col-xs-3 {
    width: 25%;
    float: left;
  }

  .row {
    margin-left: 0px;
    margin-right: 0px;
    max-width: 100%;
  }

  .col-xs-4 {
    width: 33.3333%;
    float: left;
  }

  .copyright {
    text-align: center;
    padding-right: 0px;
    text-align: center;
    margin-bottom: 15px;
    padding-left: 0px;
  }

  .copyright p{
    color: $text_white;
    padding-bottom: 1px;
    line-height: 17px;
    font-size: $font_size;
  }
  .clr {
    clear: both;
  }

  .sign_in_button a{
    color: white;
    font-size: 0.825rem;
    text-transform: uppercase;
  }
}

body .opera_footer_fix {
  position: relative;
  bottom: 0;
  width: 100%;
  z-index: 9999;
  margin-top: 12px;
}

body .opera_footer_fix a {
  margin-bottom: 0em;
  width: 100%;
  left: 0px;
  font-weight: bold;
  line-height: inherit;
}

.offer-tnc-header{
  text-align: justify;
  padding: 0 5px;
}

/***Mobile subscribe window***/
.modal-top{
  margin-top: 6px !important;
}
.modal-tr-bg{
  background: rgba(0, 0, 0, 0.45) !important;
}
#mobile-subscribe-window{
  position: fixed;
  top: 20% !important;
  width: 95%;
  margin: 0% 0% 0% 2.5%;
  border: none;
  box-shadow: none;
  border-radius: 0px;
  background: transparent;
  z-index: 9999999999 !important;
  #modal-subscribe-box{
    height: 85%;
  }
  .modal-footer{
    background: white;
    padding: 7px;
    border-radius: 0 0 10px 10px;
    #subscribe-input{
      font-size: 16px;
      margin: 5px 0px;
      border-bottom: 1px solid #dd2d6a;
      height: 25px;
      background: white;
      border-radius: 0;
      &::-webkit-input-placeholder { /* Chrome/Opera/Safari */
        color: grey;
      }
      &::-moz-placeholder { /* Firefox 19+ */
        color: grey;
      }
      &:-ms-input-placeholder { /* IE 10+ */
        color: grey;
      }
      &:-moz-placeholder { /* Firefox 18- */
        color: grey;
      }
    }
    #email-subscribe-button{
      margin: 5px 0px;
      box-shadow: 2px 2px 3px 0px grey;
      width: 100%;
      height: 30px;
      font-size: 16px;
      cursor: pointer;
    }
    #email-cancel-button{
      // border: 1px solid black;
      // width: 100%;
      // height: 32px;
      // margin: 5px 0px;
      color: black;
      font-size: 16px;
      padding: 5px 6px;
      text-decoration: underline;
      background-color: #fff;
      cursor: pointer;
    }
  }
  .newsletter-image img {
    border-radius: 10px 10px 0 0;
}
  input{
    background: #424040;
    outline: medium none;
    border: medium none;
    font-size: $font_size;
    color: rgb(255, 255, 255);
  }
  label{
    font-size: 19px;
    text-align: center;
  }
}

.sticky-coupon-banner{
  display: none;
  .wrapper{
    display: inline-flex;
    background: $dark_red;
    position: fixed;
    bottom: 0;
    width: 100%;
    z-index: 99;
    padding: 2px;
    -webkit-transform: translateX(-152px);
    transform: translateX(-152px);
    will-change: transform;
    -webkit-transition: -webkit-transform .75s ease-out;
    transition: -webkit-transform .75s ease-out;
    transition: transform .75s ease-out;
    transition: transform .75s ease-out,-webkit-transform .75s ease-out;
    .sticky-coupon-image{
      max-height: 100%;
      max-width: 100%;
      display: inline-block;
    }
    .close-sticky-coupon-banner{
      display: inline-block;
    }
    .close-button{
      color: white;
      width: 12rem;
      font-size: 16px;
      font-weight: bold;
      .close-label{
        top: 35%;
        position: absolute;
        right: 7%;
      }
    }
  }
}
#newsletter-sub-image {
  width: 100%;
  margin: 0 auto;
}

@media only screen and (min-width: 30em) {
  #mobile-subscribe-window{
    margin: 20px auto;
    width: 45%;
  }
  #newsletter-sub-image {
    height: 180px !important;
  }
  @media only screen and (min-height: 20em) {
    #newsletter-sub-image {
      height: 265px !important;
    }
  }
}

@media only screen and (min-width: 60em) {
  #mobile-subscribe-window{
    margin: 20px auto;
    width: 27%;
  }
  #newsletter-sub-image {
    height: 300px !important;
  }
}
.subscribe_text_message{
  text-align: center;
  margin-top: -3px;
  color: rgb(68, 68, 68);
}

.close_subscribe{
  width: 24px;
  font-size: 28px;
  text-align: center;
  color: #BA4800 !important;
  z-index: 100000;
}

#email_subscribe_button{
  width: 78px !important;
  float: right;
  margin-top: -55px !important;
  border: medium none;
  height: 55px !important;
  border-radius: 0px 2px 2px 0px !important;
  color: rgb(255, 255, 255) !important;
  position: absolute;
  right: 4px;
  background: rgb(222, 100, 24) none repeat scroll 0% 0% !important;
  font-size: 18px !important;
}

#subscribe_input{
  font-size: 16px !important;
  margin-bottom: 0px;
  &:focus{
    box-shadow: none;
    border-bottom: none;
  }
}

.transparent_bg {
  background: rgba(0, 0, 0, 0.45) !important;
}

.subscribe_success{
  margin-top:12px !important;
}
.feedbackDiv{
  background-color: white;
}

@media only screen and (min-width: 520px) {
  #notice_banner{
    margin: 0 auto;
    width: 360px;
  }
}
#notice_banner{
  display: none;
  background-color: #d84646;
  padding: 6px;
  margin-bottom: 10px;
  color: black;
  font-size: medium;
  text-align: center;

  .rounded_block{
    border-radius: 5px;
    background-color: #fdc5c6;
    padding: 1px 5px 6px;
  }
  .notice_header{
    padding-bottom: 1px;
    .header_text{
      font-size: 1rem;
    }
    .header_sub_text{
      font-size: 0.8rem;
    }
    #notice_close_btn{
      color: #9b0000;
      float:right;
    }
  }
  .notice_body{
    text-align: center;

    .coupon_cutout{
      border-style: dashed;
      display: inline-block;
      border-radius: 5px;
      border-width: 1.6px;
      padding: 3px 15px;

      .coupon_sub_text{
        font-size: $small_font;
        font-weight: 600;
      }
      .coupon_code{
        color: #9b0000;
        font-size: 1.4rem;
        font-weight: 600;
        line-height: 1;
      }
    }

    .coupon_message{
      display: inline-block;
      padding: 2px;
      text-align: left;

      .offer_sub_msg{
        font-size: 10px;
      }
      .offer_msg{
        font-size: $font_size;
        font-weight: 600;
      }
    }
  }
}



.footer-info {
  /*border:1px dotted grey;*/
  padding:8px;
  background-color:#fff9fa;
  margin-bottom:20px;
  margin-top:20px;
  box-sizing:border-box;
  font-size: $font_size;
  #seo_post {
    .seo-list-anchor{
      font-size: 14px;
      padding: 5px;
      line-height: 17px;
    }
    .seo-list-table {
      width:70%;
      border: 1px solid white;
    }
    .seo-list-line-height {
      line-height:30px;
    }
    .seo-list-font {
      font-size:14px;
    }
    h1, h2, h3, h4, h5, h6{
      color: $text_black;
      font-weight: bold;
    }
    h1{
      font-size: 1.1875rem;
    }
    h2{
      font-size: 1.125rem;
    }
    h3 {
      font-size: 1rem;
    }
    h4{
      font-size: 0.9375rem;
    }
    h5{
      font-size: 0.8125rem;
    }
    h6{
      font-size: 0.6875rem;
    }
    p {
      font-size: $seo_p_font_size;
      color: $text_black;
      text-align: justify;
      margin-bottom: 0.9rem;
      a{
        font-weight: bold;
      }
    }
    ul, ol{
      font-size: $seo_p_font_size;
      color: $text_black;
      text-align: justify;
      margin-bottom: 0.6rem;
      margin-left: 1.1rem;
    }
    &.read-more{
      height:7.8em;
      overflow:hidden;
    }
  }
  #popular_search{
    width: 100%;
    // height: 170px;
    color: $text_black;
    // overflow-y: scroll;
    overflow-x: hidden;
    font-size: 12px !important;
    font-weight: bold;
    position: relative;
    scroll-behavior: smooth;
    table{
      width: 100% !important;
      height: auto !important;
      table-layout: fixed;
      border: none;
      background: transparent;
    }
    tr{
      td{
        font-size: 13px !important;
        font-weight: 500;
        padding: 6px 5px;
        text-align: center;
        border: 1px solid #f1f1f1;
        background-color: #fff;
      }
    }
    h2{
      font-size: 18px !important;
      font-weight: bold;
    }
  }
  ::-webkit-scrollbar {
    -webkit-appearance: none;
    width: 3px;
  }
  ::-webkit-scrollbar-thumb {
    border-radius: 5px;
    background-color: rgba(0,0,0,.5);
    -webkit-box-shadow: 0 0 1px rgba(255,255,255,.5);
  }
  #popular_search_title{
    h3{
      font-size: 16px !important;
      font-weight: bold;
    }
  }
}

.accordion-header { 
  margin-left: 1.1em;
}

details {
  position: relative;
  margin-top: 0.2rem;
  margin-bottom: 0.5rem;
  border-bottom: 0.1px solid #dddddd;
  > :last-child {
    margin-bottom: 1rem;
  }
  &::before {
    width: 100%;
    height: 100%;
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    border-radius: inherit;
    opacity: .15;
    pointer-events: none;
    transition: opacity .2s;
    z-index: -1;
  }
  &[open] {
    background-color: #FFF;
    &::before {
      opacity: .6;
    }
  }
  *:focus {
    outline: 0;
  }
}
summary {
  padding: 1rem 2em 1rem 0;
  display: block;
  position: relative;
  font-size: 1.33em;
  font-weight: bold;
  cursor: pointer;
  &::before,
  &::after {
    width: .75em;
    height: 2px;
    position: absolute;
    top: 50%;
    right: 0;
    content: '';
    background-color: currentColor;
    text-align: right;
    transform: translateY(-50%);
    transition: transform .2s ease-in-out;
  }
  &::after {
    transform: translateY(-50%) rotate(90deg);
    [open] & {
      transform: translateY(-50%) rotate(180deg);
    }
  }
  &::-webkit-details-marker {
    display: none;
  }
  *:focus {
    outline: 0;
  }
}
#amzn_wdgt_t_8001_0{
  width: 100% !important;
  height: 100% !important;
}
#wdgt_ft{
  display: none !important;
}
#wdgt_brdr{
  height: 100% !important;
}
.footer-section-one.footer-section-two ul {
  margin-bottom: 0;
}
.fa-whatsapp{
  fill: white;
  background-color: #4dc247;
  border-radius: 50%;
  z-index: 1;
  transform: translate3d(0,0,0);
  margin-bottom: 10px;
}

#mobile_footer svg.bi {
  height: 50px;
  width: 25px;
}
.footer-component-wrapper {
  .footer-section-two {
    .social-icon-wrap {
      padding:0;
      a {
        margin: 0 6px;
        padding: 6px 0px 2px 0px;
        border-radius: 4px;
        height: 35px;
      }
    }
  }
}
.main-flex-two-container {
  padding-top: 10px;
  .flex-item-1 {
    justify-content: center !important;
    width: 100%;
    padding: 5px 0;
}
}
.main-flex-two-container {
  padding: 10px 0;
  text-align: center;
  color: #fff;
  background-color: #7b0e1d;
  line-height: 18px;
  font-size: 13px;
}
.social_icons {
  margin-top: 45px;
  padding: 0;
  margin-left: 0;
}
.desktop-accordion-btn {
  margin-left: 1.1rem;
}
.social {
  margin-top: 45px;
}
.footer-info #popular_search td a:hover {
  color: #000;
}
.footer_head {
  padding: 0;
  margin: 0;
}
@media screen and (max-width:767px) {
  .footer-info #popular_search tr {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    width: 100%;
}
.footer-info #popular_search tr td {
  width: 33%;
  font-size: 11px !important;
}
}

// Newsletter Subscription Desktop Component
.newsletter-subscription-desktop {
  background: linear-gradient(135deg, $dark_red 0%, $light_red 100%);
  padding: 40px 20px;
  margin: 0;
  display: none; // Hidden by default (mobile-first)

  .newsletter-container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 30px;
  }

  .newsletter-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    gap: 30px;
  }

  .newsletter-text {
    flex: 1;
    color: $text_white;

    .newsletter-title {
      font-size: 28px;
      font-weight: 700;
      margin: 0 0 8px 0;
      line-height: 1.2;
      font-family: "Inter", sans-serif;
    }

    .newsletter-subtitle {
      font-size: 16px;
      margin: 0;
      opacity: 0.9;
      line-height: 1.4;
    }
  }

  .newsletter-form-wrapper {
    flex: 0 0 auto;
    min-width: 400px;

    .newsletter-form {
      .form-group {
        display: flex;
        gap: 0;
        background: $text_white;
        border-radius: 6px;
        overflow: hidden;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

        .newsletter-email-input {
          flex: 1;
          padding: 16px 20px;
          border: none;
          font-size: 16px;
          background: transparent;
          color: $text_black;

          &::placeholder {
            color: $placeholder_color;
            opacity: 0.7;
          }

          &:focus {
            outline: none;
            box-shadow: none;
          }
        }

        .newsletter-submit-btn {
          background: $add_to_cart_red;
          color: $text_white;
          border: none;
          padding: 16px 30px;
          font-size: 16px;
          font-weight: 600;
          cursor: pointer;
          transition: background-color 0.3s ease;
          white-space: nowrap;

          &:hover {
            background: darken($add_to_cart_red, 10%);
          }

          &:disabled {
            background: $gray;
            cursor: not-allowed;
          }
        }
      }
    }

    .newsletter-message {
      margin-top: 12px;
      padding: 8px 12px;
      border-radius: 4px;
      font-size: 14px;
      text-align: center;
      display: none;

      &.success {
        background: rgba(255, 255, 255, 0.2);
        color: $text_white;
        border: 1px solid rgba(255, 255, 255, 0.3);
      }

      &.error {
        background: rgba(255, 255, 255, 0.9);
        color: $dark_red;
        border: 1px solid rgba(255, 255, 255, 0.3);
      }
    }
  }
}

// Show only on desktop (1024px and above)
@media screen and (min-width: 1024px) {
  .newsletter-subscription-desktop {
    display: block;
  }
}

// Responsive adjustments for smaller desktop screens
@media screen and (min-width: 1024px) and (max-width: 1200px) {
  .newsletter-subscription-desktop {
    .newsletter-content {
      flex-direction: column;
      text-align: center;
      gap: 25px;
    }

    .newsletter-text {
      .newsletter-title {
        font-size: 24px;
      }

      .newsletter-subtitle {
        font-size: 15px;
      }
    }

    .newsletter-form-wrapper {
      min-width: 350px;
    }
  }
}