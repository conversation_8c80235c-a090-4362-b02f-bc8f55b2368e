$(document).ready(function() {
  // Newsletter subscription form handling
  var $form = $('#desktop-newsletter-form');
  var $emailInput = $('#newsletter-email-input');
  var $submitBtn = $('#newsletter-submit-btn');
  var $messageDiv = $('#newsletter-message');
  
  // Email validation regex
  var emailRegex = /^([a-zA-Z0-9_.+-])+\@(([a-zA-Z0-9-])+\.)+([a-zA-Z0-9]{2,4})+$/;
  
  // Show message function
  function showMessage(message, type) {
    $messageDiv
      .removeClass('success error')
      .addClass(type)
      .text(message)
      .fadeIn();
    
    // Auto-hide success messages after 5 seconds
    if (type === 'success') {
      setTimeout(function() {
        $messageDiv.fadeOut();
      }, 5000);
    }
  }
  
  // Hide message function
  function hideMessage() {
    $messageDiv.fadeOut();
  }
  
  // Set loading state
  function setLoadingState(loading) {
    if (loading) {
      $submitBtn.prop('disabled', true).text('Subscribing...');
      $emailInput.prop('disabled', true);
    } else {
      $submitBtn.prop('disabled', false).text('Subscribe!');
      $emailInput.prop('disabled', false);
    }
  }
  
  // Form submission handler
  $form.on('submit', function(e) {
    e.preventDefault();
    
    var email = $emailInput.val().trim();
    
    // Validate email
    if (!email) {
      showMessage('Please enter your email address.', 'error');
      return;
    }
    
    if (!emailRegex.test(email) || email.endsWith('.co')) {
      showMessage('Please enter a valid email address.', 'error');
      return;
    }
    
    // Hide any previous messages
    hideMessage();
    
    // Set loading state
    setLoadingState(true);
    
    // Prepare form data
    var formData = {
      subscriptions: {
        email: email,
        source_url: window.location.href,
        appsource: 'desktop'
      }
    };
    
    // Submit form via AJAX
    $.ajax({
      url: '/subscriptions',
      type: 'POST',
      data: formData,
      dataType: 'json',
      headers: {
        'X-CSRF-Token': $('meta[name="csrf-token"]').attr('content')
      },
      success: function(response) {
        setLoadingState(false);
        
        if (response.error) {
          showMessage(response.error_message || 'An error occurred. Please try again.', 'error');
        } else {
          if (response.already_subscribed === '1') {
            showMessage(response.data, 'success');
          } else if (response.data && response.data.message && response.data.coupon_code) {
            showMessage(response.data.message + ' ' + response.data.coupon_code, 'success');
          } else {
            showMessage(response.data || 'Thank you for subscribing to our newsletter!', 'success');
          }
          
          // Clear the form
          $emailInput.val('');
          
          // Set cookie to prevent showing again
          document.cookie = 'subscribe=subscribed; path=/; max-age=' + (365 * 24 * 60 * 60); // 1 year
          
          // Hide the newsletter component after successful subscription
          setTimeout(function() {
            $('.newsletter-subscription-desktop').fadeOut();
          }, 3000);
        }
      },
      error: function(xhr, status, error) {
        setLoadingState(false);
        showMessage('An error occurred. Please try again later.', 'error');
        console.error('Newsletter subscription error:', error);
      }
    });
  });
  
  // Clear error messages when user starts typing
  $emailInput.on('input', function() {
    if ($messageDiv.hasClass('error') && $messageDiv.is(':visible')) {
      hideMessage();
    }
  });
  
  // Handle Enter key in email input
  $emailInput.on('keypress', function(e) {
    if (e.which === 13) { // Enter key
      $form.submit();
    }
  });
});
