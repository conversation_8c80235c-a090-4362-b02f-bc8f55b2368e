// This is a manifest file that'll be compiled into application.js, which will include all the files
// listed below.
//
// Any JavaScript/Coffee file within this directory, lib/assets/javascripts, vendor/assets/javascripts,
// or any plugin's vendor/assets/javascripts directory can be referenced here using a relative path.
//
// It's not advisable to add code directly here, but if you do, it'll appear at the bottom of the
// compiled file.
//
// Read Sprockets README (https://github.com/rails/sprockets#sprockets-directives) for details
// about supported directives.
// Foundation js used is custom build, only below mentioned js foundation libraries will work.
// To add new foundation js library include file in that view only not here.
//
//= require jquery
//= require jquery_ujs
//= require modernizr.min.js
//= require foundation/foundation-custom
//= require foundation/accordion
//= require foundation/alert
//= require foundation/offcanvas
//= require foundation/reveal
//= require foundation/tab
//= require 'foundation/tooltip'
//= require blaze
//= require turbolinks
//= require common
//= require marketing
//= require opera_mini_fix
//= require custom-lazy-load
//= require wishlist
//= require product_video_controls
//= require cleave
//= require designs
//= require product
//= require product_recommendation
//= require pushengage
//= require newsletter_subscription_desktop

// Code Below is minified and written inline in application.html.haml it is here only for reference
// var addEvent, afterWindowOrTrubolinksLoad, loadScript, loadScriptAfterLoad, loadStyle, unbxdTrack, unbxdTrackPendingArray;

// addEvent = function(t, n, e) {
//   if (t.attachEvent) {
//     return t.attachEvent('on' + n, e);
//   } else {
//     return t.addEventListener(n, e, !1);
//   }
// };

// loadScript = function(url, callable, force) {
//   var ele, script;
//   ele = document.querySelectorAll('script[src="' + url + '"]')[0]
//   if (force === true || !ele) {
//     if (ele) {
//       ele.parentNode.removeChild(ele);
//     }
//     script = document.createElement('script');
//     script.type = 'text/javascript';
//     if (typeof callable === 'function') {
//       if (script.readyState) {
//         script.onreadystatechange = function() {
//           if ('loaded' === script.readyState || 'complete' === script.readyState) {
//             script.onreadystatechange = null;
//             callable();
//           }
//         };
//       } else {
//         script.onload = function() {
//           callable();
//         };
//       }
//     }
//     script.src = url;
//     document.getElementsByTagName('head')[0].appendChild(script);
//   }
// };

// afterWindowOrTrubolinksLoad = function(callable) {
//   addEvent(window, 'load', callable);
//   document.addEventListener('turbolinks:render', callable);
// };

// loadScriptAfterLoad = function(url, callable, force) {
//   addEvent(window, 'load', function() {
//     loadScript(url, callable, force);
//   });
// };

// unbxdTrack = function(event_name, event_details) {
//   unbxdTrackPendingArray.push([event_name, event_details]);
// };

// loadStyle = function(url, attributes) {
//   var key, link_tag;
//   if (!document.querySelector('link[href="' + url + '"]')) {
//     link_tag = document.createElement('link');
//     link_tag.rel = 'stylesheet';
//     link_tag.href = url;
//     for (var attribute in attributes) {
//       link_tag.setAttribute(attribute, attributes[attribute]);
//     }
//     document.getElementsByTagName('head')[0].appendChild(link_tag);
//   }
// };
// unbxdTrackPendingArray = [];



// $(document).on("click", ".ga-sign-in-tracking", function () {
    // ga('send', 'event', 'footer-component', 'user-signed-in', 'user-signed-in');  
// })

// $(document).on("click", ".ga-sign-out-tracking", function () {
    // ga('send', 'event', 'footer-component', 'user-signed-out', 'user-signed-out');  
// })
setTimeout(function(){
    $('.row.flash-message').fadeOut('slow');
  }, 6000); 