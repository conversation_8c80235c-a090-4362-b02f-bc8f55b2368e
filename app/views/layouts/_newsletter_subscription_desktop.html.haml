.newsletter-subscription-desktop.d_web
  .newsletter-container
    .newsletter-content
      .newsletter-text
        %h2.newsletter-title Sign Up to Receive Our Updates
        %p.newsletter-subtitle Be the first to know about latest offers and discounts on Mirraw
      .newsletter-form-wrapper
        = form_with url: subscriptions_path, method: :post, local: false, id: 'desktop-newsletter-form', class: 'newsletter-form' do |form|
          .form-group
            = form.email_field 'subscriptions[email]', placeholder: 'Enter your Email Address', class: 'newsletter-email-input', id: 'newsletter-email-input', required: true
            = form.submit 'Subscribe!', class: 'newsletter-submit-btn', id: 'newsletter-submit-btn'
          = form.hidden_field 'subscriptions[source_url]', value: request.url
          = form.hidden_field 'subscriptions[appsource]', value: 'desktop'
        .newsletter-message#newsletter-message
