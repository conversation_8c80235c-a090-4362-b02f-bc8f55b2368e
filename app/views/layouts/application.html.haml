!!!
%html{xmlns: '//www.w3.org/1999/xhtml', 'xml:lang' => 'en', lang: 'en', 'xmlns:fb' => '//ogp.me/ns/fb#'}
  %head
    = render partial: 'layouts/lcp_header'
    = render partial: 'layouts/seo'
    = render partial: 'layouts/json_microdata'
    -# minfied for reference see application.js
    - if current_account.present?
      :javascript
        window.dataLayer = window.dataLayer || [];
        dataLayer.push({
          user_id: "#{current_account.id}"
        });

    - if ["production"].include?(Rails.env)
      / Google Tag Manager
      - unless params[:controller] == 'store' && params[:action] == 'catalog_page' && request.path.include?('/search')
        :javascript
          (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
          new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
          j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
          'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
          })(window,document,'script','dataLayer','#{ENV['GTM_CONTAINER_ID']}');
        / End Google Tag Manager
    :javascript
      var global_tracking = {
        country_code : "#{@country_code}",
        ecomm_pagetype: "#{@ecomm_pagetype}",
        ecomm_category: "#{@ecomm_category}"
      }
    - if @ga_login_events.present?
      :javascript
        window.dataLayer = window.dataLayer || [];
        #{ ga_event_script_hash(@ga_login_events) }

    :javascript
      var addEvent,afterWindowOrTrubolinksLoad,loadScript,loadScriptAfterLoad,loadStyle,unbxdTrack,unbxdTrackPendingArray;addEvent=function(a,b,c){return a.attachEvent?a.attachEvent("on"+b,c):a.addEventListener(b,c,!1)};
      loadScript=function(a,b,c){var e=document.querySelectorAll('script[src="'+a+'"]')[0];if(!0===c||!e){e&&e.parentNode.removeChild(e);var d=document.createElement("script");d.type="text/javascript";"function"===typeof b&&(d.readyState?d.onreadystatechange=function(){if("loaded"===d.readyState||"complete"===d.readyState)d.onreadystatechange=null,b()}:d.onload=function(){b()});d.src=a;document.getElementsByTagName("head")[0].appendChild(d)}};
      afterWindowOrTrubolinksLoad=function(a){addEvent(window,"load",a);document.addEventListener("turbolinks:render",a)};loadScriptAfterLoad=function(a,b,c){addEvent(window,"load",function(){loadScript(a,b,c)})};unbxdTrack=function(a,b){unbxdTrackPendingArray.push([a,b])};loadStyle=function(a,b){if(!document.querySelector('link[href="'+a+'"]')){var c=document.createElement("link");c.rel="stylesheet";c.href=a;for(var e in b)c.setAttribute(e,b[e]);document.getElementsByTagName("head")[0].appendChild(c)}};
      unbxdTrackPendingArray=[];
    -# %link{:as => "font", :crossorigin => "", :href => asset_path('Lato-Regular.woff2'), :media => "all", :rel => "preload", :type => "font/woff2"}
    -# %link{:as => "font", :crossorigin => "", :href => asset_path('foundation-icons.woff'), :media => "all", :rel => "preload", :type => "font/woff"}
    - inline_header = 'inline_header_red.css'
    
    = stylesheet_link_tag 'inline_header_red.css'
    
    - unless (design_details_page? || offer_message_pages?)
      = javascript_include_tag 'application', 'data-turbolinks-track' => 'reload'
    - else
      = javascript_include_tag 'application', :defer => true, 'data-turbolinks-track' => 'reload'
    = render partial: 'layouts/ga'
    = render partial: 'layouts/assets'
    = csrf_meta_tags
    /Canonical Links***********************
    - canonical_path = @canonical_path || content_for(:canonical_path) || request.path
    - if (params[:controller] == 'store' && params[:action] == 'catalog_page') || (params[:controller] == 'reviews' && params[:action] == 'site_review')
      - request_params = params.dup
      - request_params.delete(:category_parent_id)
      - request_params.delete(:items_per_page)
      - request_params[:page] = (request_params[:page].presence || 1).to_i + 1
      %link{href: "#{SITE_PROTOCOL}#{ENV['MOBILE_SITE_URL']}#{canonical_path}?page=#{request_params[:page]}",rel: "next"}
      - request_params[:page] = request_params[:page].to_i - 1
      -if request_params[:page] == 1
        %link{href: "#{SITE_PROTOCOL}#{DESKTOP_SITE_URL}#{canonical_path}",rel: "canonical"}
      -elsif request_params[:page] == 2
        %link{href: "#{SITE_PROTOCOL}#{DESKTOP_SITE_URL}#{canonical_path}?page=#{request_params[:page]}",rel: "canonical"}
        %link{href: "#{SITE_PROTOCOL}#{ENV['MOBILE_SITE_URL']}#{canonical_path}",rel: "prev"}
      -else
        %link{href: "#{SITE_PROTOCOL}#{DESKTOP_SITE_URL}#{canonical_path}?page=#{request_params[:page]}",rel: "canonical"}
        - request_params[:page] = request_params[:page].to_i - 1
        %link{href: "#{SITE_PROTOCOL}#{ENV['MOBILE_SITE_URL']}#{canonical_path}?page=#{request_params[:page]}",rel: "prev"}
    -else
      %link{href: "#{SITE_PROTOCOL}#{DESKTOP_SITE_URL}#{canonical_path}",rel: "canonical"}
    /*************************************
    - if offer_message_pages?
      - request_split = request.fullpath.split('?')
    = favicon_link_tag 'favicon.png'

    - if ["production"].include?(Rails.env)
      = render partial: 'layouts/fb_analytics'

    :javascript
        var UnbxdSiteName = "#{MirrawMobile::Application.config.unbxd[:UNBXD_SITE_KEY]}"; // Replace the value with the Site Key.
        var UnbxdApiKey = "#{MirrawMobile::Application.config.unbxd[:UNBXD_API_KEY]}"; // Replace the value with API key
        (function() {
          var ubx = document.createElement('script');
          ubx.type = 'text/javascript';
          ubx.async = true;
          ubx.src = '//libraries.unbxdapi.com/ua-js/v1.0.0/uaLibrary.js';
          (document.getElementsByTagName('head')[0] || document.getElementsByTagName('body')[0]).appendChild(ubx);
        })();
    - if has_search_bar?
      -if firefox_browser?
        = async_stylesheet_tag('autosuggest');
      -else
        = stylesheet_link_tag 'autosuggest', rel: 'preload', as: 'style',  onload: "this.onload=null;this.rel='stylesheet'"
    - elsif !is_mobile_view?
      = stylesheet_link_tag 'autosuggest', rel: 'preload', as: 'style',  onload: "this.onload=null;this.rel='stylesheet'"

    - if CRITEO_ACCOUNT_ID[@country_code].present?
      :javascript
        afterWindowOrTrubolinksLoad(function(){
          if((typeof(criteo_dynamic_object) != 'undefined') && typeof(criteo_push_data) == 'function'){
            delete(window.criteo_q);
            loadScript("//dynamic.criteo.com/js/ld/ld.js?a=#{CRITEO_ACCOUNT_ID[@country_code]}", function(){criteo_push_data();}, true)
          }
        })
    -# - if (REMARKETING_CONVERSION_ID.present? || ADWORD_CONVERSION_TRACKING_IDS.present?)
      %script
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments)};
        - conversion_ids = ADWORD_CONVERSION_TRACKING_IDS.try(:keys).to_a
        - if REMARKETING_CONVERSION_ID.present?
          - conversion_ids.push(REMARKETING_CONVERSION_ID).uniq!
        - conversion_ids.each do |conversion_id|
          gtag('config', 'AW-#{conversion_id}', {'send_page_view': false});
        gtag('js', new Date());
        loadScriptAfterLoad("https://www.googletagmanager.com/gtag/js?id=AW-#{REMARKETING_CONVERSION_ID}")

  - if turbolinks_active_page?
    %body{style: 'overflow: scroll;'}
  - else
    %body{style: 'overflow: scroll;', data: {turbolinks: 'false'}}
  = render partial: 'layouts/criteo_tags' if CRITEO_ACCOUNT_ID[@country_code].present?

  = yield :head
  = yield :page_specific_css_body
  - if ["production"].include?(Rails.env)
    / Google Tag Manager (noscript)
    %noscript
      %iframe{height: "0", src: "https://www.googletagmanager.com/ns.html?id=#{ENV['GTM_CONTAINER_ID']}", style: "display:none;visibility:hidden", width: "0"}
    / End Google Tag Manager (noscript)
    %noscript
      .jswarning
        This website will only function properly with JavaScript enabled. Please enable javascript in your browser and then refresh the page.
  - unless check_for_alternate_tab?
    = render partial: '/layouts/menu1'

  - if @country_code == 'IN' && login_page? && is_mobile_view?
    .new-top-bar
      .border-top.fixed
      = link_to '', session[:previous_url], class: 'fi-x'
      .logo
        %a{href: '/'}
          = image_tag('mirraw-logo-signin.png', alt: '')
    = yield

  - else # condition for redesigned login page
    .page{class: "#{params[:controller]}_#{params[:action]}"}
      .off-canvas-wrap{data:{offcanvas: ''}}
        .inner-wrap
          -if params[:api_layout] == 'true'

          -else 
            - unless is_mobile_view?
              .desk_web
                = render partial: '/layouts/header'
            .border-top.m_web{class: [("android_menu_fix" if android_four_browser?), ("fixed" if !has_search_bar?)]}
              = render partial: '/layouts/horizontal_menu'
              - if has_search_bar?
                .fixed-header.border-top{class: ("android_menu_fix" if android_four_browser?)}
                  = render partial: '/layouts/static_horizontal_menu'
          %section#coupon-main.main-section{class:[ (:android_fix if native_android_browser?), (:safari_seven_fix if (safari_seven_browser? || safari_four_lower?)), ('with-menu' if !check_for_alternate_tab? && !has_search_bar?), ('no-search-bar' unless has_search_bar?) ] }
            #main-section{class:[ (:opera_top_margin if opera_mini_browser?), (:safari_login_fix if (safari_four_lower? || safari_in_app)), (:android_top_margin if android_four_browser?) ] }
              #container
                #notice_banner
                  .rounded_block
                    .notice_header
                      %span.header_text Welcome!
                      %span.header_sub_text You have successfully subscribed
                      %a#notice_close_btn x
                    .notice_body
                      .coupon_cutout
                        .coupon_sub_text USE COUPON CODE
                        .coupon_code
                      .coupon_message
                        .offer_sub_msg Get
                        .offer_msg
                = yield
                - #{data:{no: {turbolink: ''}}}
              - if !checkout_flow_page? && !checkout_cart_login? && !login_page? && cookies[:subscribe].nil?
                = render partial: 'layouts/newsletter_subscription_desktop'
              = render partial: 'layouts/footer'
              - if !uc_browser? && !opera_mini_browser? && !(android_four_browser?) && !(params[:api_layout] == 'true')
                = render partial: 'footer/footer_new'
          .exit-off-canvas
      - if uc_browser? || opera_mini_browser? || android_four_browser?
        = render partial: 'layouts/footer_custom'

  - if ENV['ENABLE_SUBSCRIPTION'].to_s == 'true' && params[:controller] == 'pages' && params[:action] == 'home'
    - if opera_mini_browser?
      = render partial: 'layouts/subscription_mobile_opera'
    - elsif !(uc_browser?)
      :javascript
        afterWindowOrTrubolinksLoad(function(){loadScript("#{asset_url('subscription_mobile.js')}");})

  = render partial: 'layouts/remarketing'
  = render partial: '/browser_fix/uc_browser_fix'
  = render partial: '/browser_fix/opera_mini_fix'
  = yield :page_specific_js_body

  :javascript
    afterWindowOrTrubolinksLoad(function(){
      loadScript("#{asset_url('unbxd_autosuggest.js')}", 'async', function(){});
    })

  = render partial: 'layouts/ga_bottom'

  -if controller.controller_name == 'pages' && action_name == 'home'
    -if ENABLE_WIZGO["enable"] == true
      = render partial: '/layouts/whatsapp_partial', locals: {text: "Hi! Could you help me with a few queries!"}
